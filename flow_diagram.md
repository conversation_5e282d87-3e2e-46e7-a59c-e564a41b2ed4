# Charles Log Parser Project Flow Diagram

```mermaid
graph TD
    %% Main components and flows
    User([User]) -->|Provides .chlsj file| Parser
    User -->|Views| Dashboard
    
    %% Core components
    subgraph "Core Components"
        Parser[Parser Tools] -->|Process data| LogData[Parsed Data]
        LogData -->|Generate| Dashboard[Visualization]
    end
    
    %% Parser tools
    Parser -->|Basic parse| Basic[parse_charles_log]
    Parser -->|Save results| Save[parse_and_save_charles_log]
    Parser -->|Large files| Large[read_large_file_part]
    Parser -->|Filter by host| Filter[parse_by_host]
    
    %% Output options
    Dashboard -->|HTML| HTMLView[Browser View]
    Dashboard -->|Streamlit| StreamView[Interactive View]
    
    %% Output formats
    LogData -->|Raw| RawJSON[Raw JSON]
    LogData -->|Summary| SummaryStats[Statistics]
    LogData -->|Detailed| DetailedView[Detailed Data]
    
    %% Styling
    classDef core fill:#f9f,stroke:#333,stroke-width:2px;
    classDef output fill:#bbf,stroke:#333,stroke-width:1px;
    classDef tool fill:#bfb,stroke:#333,stroke-width:1px;
    
    class Parser,LogData core;
    class Dashboard,HTMLView,StreamView output;
    class Basic,Save,Large,Filter tool;
```

# Simplified Flow Diagram

For a simpler understanding of the project workflow:

```mermaid
graph TD
    A[Charles Proxy] -->|Export logs as .chlsj| B[Log File]
    B --> C{Parse Log}
    C -->|Basic parsing| D[All Entries]
    C -->|Filter by host| E[Host-specific Entries]
    
    D --> F[Generate Output]
    E --> F
    
    F -->|Raw format| G[Raw JSON]
    F -->|Summary format| H[Summary Statistics]
    F -->|Detailed format| I[Detailed Entries]
    
    G --> J[Save to File/View Dashboard]
    H --> J
    I --> J
    
    style A fill:#f9d5e5,stroke:#333,stroke-width:2px
    style B fill:#eeeeee,stroke:#333,stroke-width:2px
    style C fill:#e3f2fd,stroke:#333,stroke-width:2px
    style D fill:#e8f5e9,stroke:#333,stroke-width:2px
    style E fill:#e8f5e9,stroke:#333,stroke-width:2px
    style F fill:#fff9c4,stroke:#333,stroke-width:2px
    style G fill:#f5f5f5,stroke:#333,stroke-width:2px
    style H fill:#f5f5f5,stroke:#333,stroke-width:2px
    style I fill:#f5f5f5,stroke:#333,stroke-width:2px
    style J fill:#d1c4e9,stroke:#333,stroke-width:2px
```

## Project Components Explanation

### Core Components
- **server.py**: MCP server implementing the parser tools
- **client.py**: Client interface for users to parse Charles log files
- **parse_charles_log**: Tool to parse a log file and return data
- **parse_and_save_charles_log**: Tool to parse a log file and save results
- **read_large_file_part**: Tool to process large files in chunks
- **view_charles_log_dashboard**: Tool to visualize parsed data in HTML dashboard

### Dashboard Components
- **HTML Dashboard**: Generated by view_charles_log_dashboard tool
  - **dashboard.html**: HTML template for the dashboard
  - **dashboard.css**: Styling for the dashboard
  - **dashboard.js**: Interactive charts using Chart.js
- **Streamlit Dashboard**: Interactive Python-based dashboard
  - **dashboard.py**: Streamlit app for data visualization
  - **run_dashboard.sh/.bat**: Scripts to launch the Streamlit dashboard

### Data Flow
1. User provides a Charles log file (.chlsj)
2. File is parsed using MCP tools (parse_charles_log or parse_and_save_charles_log)
3. Results are either:
   - Returned directly as JSON data
   - Saved to a JSON file in the output directory
4. Visualization options:
   - HTML Dashboard: Opens directly in browser with interactive charts
   - Streamlit Dashboard: User runs script for advanced data exploration
   - Simple Dashboard: Alternative lightweight HTML visualization

### Alternative Flows
- **Large File Processing**: For very large log files, using large_file_example.py 
  for chunk-by-chunk processing
- **Simple Dashboard**: For environments with limited dependencies 